// Main JavaScript for City Camera System UI

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded. Initializing application...');

    // DOM Elements - Cache all elements for better performance
    const mapContainer = document.getElementById('map-container');
    const mapImage = document.getElementById('map-image');
    const zoomInBtn = document.getElementById('zoom-in');
    const zoomOutBtn = document.getElementById('zoom-out');
    const toggleGridBtn = document.getElementById('toggle-grid');
    const rotateOrientationBtn = document.getElementById('rotate-orientation');
    const gridOverlay = document.getElementById('grid-overlay');
    const mapSelectorToggle = document.getElementById('map-selector-toggle');
    const mapStyleSelector = document.getElementById('map-style-selector');
    const mapOptions = document.querySelectorAll('.map-option');
    const currentMapType = document.getElementById('current-map-type');
    const currentMapDisplay = document.getElementById('current-map-display');
    const zoomLevelDisplay = document.getElementById('zoom-level');
    const statusInfo = document.getElementById('status-info');

    // GTA V/FiveM coordinate system parameters (based on your Leaflet code)
    const center_x = 117.3;
    const center_y = 172.8;
    const scale_x = 0.02072;
    const scale_y = 0.0205;

    // Map state - initial values that will be properly set in initializeMap()
    let mapState = {
        zoom: 1,
        translateX: 0,
        translateY: 0,
        isDragging: false,
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
        mapType: 'satellite',
        orientation: 'portrait_flipped', // portrait_flipped, landscape, portrait, landscape_flipped
        gridEnabled: true, // grid visibility state
        cameras: [
            { id: 1, x: 1855.23, y: 3710.52, type: 'police', name: 'Sandy Shores Police Department', active: true, icon: 'fa-shield-alt' },
            { id: 2, x: -448.23, y: 6012.35, type: 'police', name: 'Paleto Bay Sheriff\'s Office', active: true, icon: 'fa-shield-alt' },
            { id: 3, x: 642.75, y: 267.89, type: 'store', name: 'Downtown Vinewood', active: true, icon: 'fa-store' },
            { id: 4, x: 434.17, y: -981.97, type: 'police', name: 'Los Santos Police Department', active: true, icon: 'fa-shield-alt' },
            { id: 5, x: -1201.12, y: -1503.76, type: 'beach', name: 'Vespucci Beach', active: true, icon: 'fa-umbrella-beach' },
            { id: 6, x: 1702.28, y: 4933.21, type: 'store', name: 'Grapeseed', active: true, icon: 'fa-store' },
            { id: 7, x: 501.35, y: 5603.67, type: 'park', name: 'Mount Chiliad', active: true, icon: 'fa-mountain' },
            { id: 8, x: 546.61, y: 2662.87, type: 'gas', name: 'Harmony', active: true, icon: 'fa-gas-pump' },
            { id: 9, x: 180.43, y: -1700.46, type: 'apartment', name: 'Davis', active: true, icon: 'fa-building' },
            { id: 10, x: 1159.77, y: -326.77, type: 'park', name: 'Mirror Park', active: true, icon: 'fa-tree' },
            { id: 11, x: 128.86, y: -1298.94, type: 'special', name: 'SLT Location', active: true, icon: 'fa-star' }
        ],
        selectedCamera: null
    };

    // ===== EVENT LISTENERS =====

    // Zoom in button
    zoomInBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        zoomMap(0.1);
        console.log('Zoomed in');
    });

    // Zoom out button
    zoomOutBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        zoomMap(-0.1);
        console.log('Zoomed out');
    });



    // Toggle grid button
    toggleGridBtn.addEventListener('click', (e) => {
        e.stopPropagation();

        // Toggle active state on button
        toggleGridBtn.classList.toggle('active');

        // Apply grid visibility based on button state
        if (toggleGridBtn.classList.contains('active')) {
            // Show grid with smooth fade-in
            gridOverlay.classList.remove('hidden');
            gridOverlay.style.opacity = '1';

            // Store grid state in map state
            mapState.gridEnabled = true;
            console.log('Grid overlay enabled');
        } else {
            // Hide grid with smooth fade-out
            gridOverlay.style.opacity = '0';

            setTimeout(() => {
                if (!mapState.gridEnabled) {
                    gridOverlay.classList.add('hidden');
                }
            }, 300);

            // Store grid state in map state
            mapState.gridEnabled = false;
            console.log('Grid overlay disabled');
        }
    });

    // Rotate orientation button - cycle through 4 orientations with single click
    rotateOrientationBtn.addEventListener('click', (e) => {
        e.stopPropagation();

        // Cycle through orientations: portrait_flipped -> landscape -> portrait -> landscape_flipped -> portrait_flipped
        switch (mapState.orientation) {
            case 'portrait_flipped':
                mapState.orientation = 'landscape';
                console.log('Switched to landscape orientation');
                break;
            case 'landscape':
                mapState.orientation = 'portrait';
                console.log('Switched to portrait orientation');
                break;
            case 'portrait':
                mapState.orientation = 'landscape_flipped';
                console.log('Switched to landscape flipped orientation');
                break;
            case 'landscape_flipped':
            default:
                mapState.orientation = 'portrait_flipped';
                console.log('Switched to portrait flipped orientation');
                break;
        }

        // Reset position when changing orientation to avoid confusion
        mapState.translateX = 0;
        mapState.translateY = 0;

        // Update transform to apply rotation
        updateMapTransform();

        // Update direction indicators based on new orientation
        updateDirectionIndicators();
    });

    // Map selector toggle button
    mapSelectorToggle.addEventListener('click', (e) => {
        e.stopPropagation();

        // Toggle the visibility of the map style selector
        mapStyleSelector.classList.toggle('hidden');

        // Get all icon buttons except the map selector
        const otherIcons = Array.from(document.querySelectorAll('.icon-btn')).filter(
            btn => btn !== mapSelectorToggle
        );

        // If opening the menu
        if (!mapStyleSelector.classList.contains('hidden')) {
            // Hide the map selector icon
            mapSelectorToggle.style.opacity = '0';
            mapSelectorToggle.style.visibility = 'hidden';

            // Position the menu below the black bar and aligned with icons
            mapStyleSelector.style.transform = 'none';
            mapStyleSelector.style.top = '40px';
            mapStyleSelector.style.right = '10px';
            mapStyleSelector.style.left = 'auto';

            // Hide status info
            if (statusInfo) {
                statusInfo.classList.add('hidden');
            }

            console.log('Menu positioned and status info hidden');

            // Shift other icons to the left (more to the left of the menu)
            otherIcons.forEach(icon => {
                icon.style.transform = 'translateX(-150px)';
                icon.style.transition = 'transform 0.2s ease';
            });

            // Preload all map preview images
            const previewImages = document.querySelectorAll('.map-option img');
            previewImages.forEach(img => {
                img.fetchPriority = 'high';
                const preloader = new Image();
                preloader.src = img.src;
            });
        } else {
            // Show the map selector icon again
            mapSelectorToggle.style.opacity = '1';
            mapSelectorToggle.style.visibility = 'visible';

            // Show status info
            if (statusInfo) {
                statusInfo.classList.remove('hidden');
            }

            // Reset other icons position
            otherIcons.forEach(icon => {
                icon.style.transform = 'translateX(0)';
            });
        }

        console.log(`Map style selector: ${mapStyleSelector.classList.contains('hidden') ? 'hidden' : 'visible'}`);
    });

    // Close map selector when clicking outside
    document.addEventListener('click', (e) => {
        // If clicking outside the map selector and not on the toggle button
        if (!mapStyleSelector.contains(e.target) && e.target !== mapSelectorToggle && !mapSelectorToggle.contains(e.target)) {
            // Hide the menu
            mapStyleSelector.classList.add('hidden');

            // Reset transform to default
            mapStyleSelector.style.transform = 'none';

            // Show the map selector icon again
            mapSelectorToggle.style.opacity = '1';
            mapSelectorToggle.style.visibility = 'visible';

            // Show status info
            if (statusInfo) {
                statusInfo.classList.remove('hidden');
            }

            // Reset other icons position
            const otherIcons = Array.from(document.querySelectorAll('.icon-btn')).filter(
                btn => btn !== mapSelectorToggle
            );

            otherIcons.forEach(icon => {
                icon.style.transform = 'translateX(0)';
            });
        }
    });

    // Map style options
    mapOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            e.stopPropagation();

            // Remove active class from all options
            mapOptions.forEach(opt => opt.classList.remove('active'));

            // Add active class to selected option
            option.classList.add('active');

            // Get the map type from the data attribute
            const mapType = option.getAttribute('data-map');

            // Change the map style
            changeMapStyle(mapType);

            // Hide the selector after selection with a small delay for better UX
            setTimeout(() => {
                // Hide the menu
                mapStyleSelector.classList.add('hidden');

                // Reset transform to default
                mapStyleSelector.style.transform = 'none';

                // Show the map selector icon again
                mapSelectorToggle.style.opacity = '1';
                mapSelectorToggle.style.visibility = 'visible';

                // Show status info
                if (statusInfo) {
                    statusInfo.classList.remove('hidden');
                }

                // Reset other icons position
                const otherIcons = Array.from(document.querySelectorAll('.icon-btn')).filter(
                    btn => btn !== mapSelectorToggle
                );

                otherIcons.forEach(icon => {
                    icon.style.transform = 'translateX(0)';
                });
            }, 300);
        });
    });

    // Map dragging
    mapContainer.addEventListener('mousedown', startDrag);
    mapContainer.addEventListener('touchstart', (e) => {
        // Prevent default behavior to avoid scrolling
        if (e.cancelable) {
            e.preventDefault();
        }

        const touch = e.touches[0];
        // Create a custom event object with only the properties we need
        const customEvent = {
            clientX: touch.clientX,
            clientY: touch.clientY,
            preventDefault: function() {} // Add empty preventDefault function
        };
        startDrag(customEvent);
    }, { passive: false }); // Set passive to false to allow preventDefault

    // Mouse wheel zoom
    mapContainer.addEventListener('wheel', (e) => {
        e.preventDefault();
        const delta = e.deltaY > 0 ? -0.25 : 0.25;
        zoomMap(delta);
    }, { passive: false }); // Explicitly set passive to false to allow preventDefault

    // ===== FUNCTIONS =====

    // Variables to track mouse state
    let isMouseDown = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    // Start dragging the map
    function startDrag(e) {
        // Set mouse state
        isMouseDown = true;

        // Store current mouse position
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;

        // Calculate starting position
        mapState.startX = e.clientX - mapState.translateX;
        mapState.startY = e.clientY - mapState.translateY;

        // Change cursor only when actively dragging
        mapContainer.style.cursor = 'grabbing';

        // Add event listeners for drag movement and release
        document.addEventListener('mousemove', drag);
        document.addEventListener('touchmove', touchDrag, { passive: false }); // Set passive to false to allow preventDefault
        document.addEventListener('mouseup', stopDrag);
        document.addEventListener('touchend', stopDrag);

        // Prevent default behavior to avoid text selection
        if (e.cancelable && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }
    }

    // Drag the map - with smooth movement
    function drag(e) {
        // Only move if mouse button is pressed
        if (!isMouseDown) {
            stopDrag();
            return;
        }

        // Calculate movement delta
        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;

        // Update last mouse position
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;

        // Update map position based on orientation
        switch (mapState.orientation) {
            case 'portrait':
                // In portrait mode, swap and invert the deltas for natural movement
                mapState.translateX += deltaY; // Y movement affects X in portrait
                mapState.translateY -= deltaX; // X movement affects Y in portrait (inverted)
                break;
            case 'portrait_flipped':
                // In flipped portrait mode, swap deltas but don't invert
                mapState.translateX -= deltaY; // Y movement affects X in portrait (inverted)
                mapState.translateY += deltaX; // X movement affects Y in portrait
                break;
            case 'landscape_flipped':
                // In flipped landscape mode, invert both deltas
                mapState.translateX -= deltaX; // Inverted X
                mapState.translateY -= deltaY; // Inverted Y
                break;
            case 'landscape':
            default:
                // Normal movement in landscape mode
                mapState.translateX += deltaX;
                mapState.translateY += deltaY;
                break;
        }

        // Update the transform immediately
        updateMapTransform();

        // Prevent default behavior
        if (e.cancelable && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }
    }

    // Handle touch drag
    function touchDrag(e) {
        if (!e.touches.length) {
            stopDrag();
            return;
        }

        const touch = e.touches[0];

        // Calculate movement delta (if we have previous position)
        if (lastMouseX !== 0 && lastMouseY !== 0) {
            const deltaX = touch.clientX - lastMouseX;
            const deltaY = touch.clientY - lastMouseY;

            // Update map position based on orientation
            switch (mapState.orientation) {
                case 'portrait':
                    // In portrait mode, swap and invert the deltas for natural movement
                    mapState.translateX += deltaY; // Y movement affects X in portrait
                    mapState.translateY -= deltaX; // X movement affects Y in portrait (inverted)
                    break;
                case 'portrait_flipped':
                    // In flipped portrait mode, swap deltas but don't invert
                    mapState.translateX -= deltaY; // Y movement affects X in portrait (inverted)
                    mapState.translateY += deltaX; // X movement affects Y in portrait
                    break;
                case 'landscape_flipped':
                    // In flipped landscape mode, invert both deltas
                    mapState.translateX -= deltaX; // Inverted X
                    mapState.translateY -= deltaY; // Inverted Y
                    break;
                case 'landscape':
                default:
                    // Normal movement in landscape mode
                    mapState.translateX += deltaX;
                    mapState.translateY += deltaY;
                    break;
            }

            // Update the transform immediately
            updateMapTransform();
        }

        // Update last touch position
        lastMouseX = touch.clientX;
        lastMouseY = touch.clientY;

        // Prevent default behavior to stop scrolling
        if (e.cancelable) {
            e.preventDefault();
        }
    }

    // Stop dragging
    function stopDrag() {
        // Reset mouse state
        isMouseDown = false;
        lastMouseX = 0;
        lastMouseY = 0;

        // Reset cursor to default
        mapContainer.style.cursor = 'default';

        // Remove event listeners
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('touchmove', touchDrag);
        document.removeEventListener('mouseup', stopDrag);
        document.removeEventListener('touchend', stopDrag);
    }

    // Zoom the map
    function zoomMap(delta) {
        const newZoom = mapState.zoom + delta;
        const zoomPercent = Math.round((newZoom - 1) * 100);

        // Prevent zoom out below 0% (which is a scale of 1)
        // And prevent extremely large values
        if (newZoom >= 1 && newZoom <= 10) {
            mapState.zoom = newZoom;
            updateMapTransform();

            zoomLevelDisplay.textContent = `${zoomPercent}%`;
            console.log(`Zoom level: ${zoomPercent}%`);
        } else if (newZoom < 1) {
            // If trying to zoom out below 0%, set to exactly 0%
            mapState.zoom = 1;
            zoomLevelDisplay.textContent = '0%';
            updateMapTransform();
            console.log('Zoom level limited to 0%');
        }
    }

    // Update map transform with smooth animation
    function updateMapTransform() {
        // Get map dimensions
        const mapRect = mapImage.getBoundingClientRect();

        // Calculate map boundaries to prevent it from disappearing
        // We want to ensure at least 75% of the map is always visible
        const minVisiblePercent = 0.75;

        // Calculate the maximum allowed translation based on map and container dimensions
        // This ensures that at least minVisiblePercent of the map is always visible
        const maxTranslateX = (mapRect.width * mapState.zoom * (1 - minVisiblePercent)) / 2;
        const maxTranslateY = (mapRect.height * mapState.zoom * (1 - minVisiblePercent)) / 2;

        // Limit translation to keep map partially visible
        mapState.translateX = Math.max(-maxTranslateX, Math.min(maxTranslateX, mapState.translateX));
        mapState.translateY = Math.max(-maxTranslateY, Math.min(maxTranslateY, mapState.translateY));

        // Apply different transformations based on orientation
        switch (mapState.orientation) {
            case 'landscape':
                // Standard landscape mode
                mapImage.style.transform = `translate3d(${mapState.translateX}px, ${mapState.translateY}px, 0) scale(${mapState.zoom})`;
                break;
            case 'landscape_flipped':
                // Flipped landscape mode (180 degrees)
                mapImage.style.transform = `rotate(180deg) translate3d(${mapState.translateX}px, ${mapState.translateY}px, 0) scale(${mapState.zoom})`;
                break;
            case 'portrait':
                // Standard portrait mode (90 degrees)
                mapImage.style.transform = `rotate(90deg) translate3d(${mapState.translateX}px, ${mapState.translateY}px, 0) scale(${mapState.zoom})`;
                break;
            case 'portrait_flipped':
                // Flipped portrait mode (270 degrees)
                mapImage.style.transform = `rotate(270deg) translate3d(${mapState.translateX}px, ${mapState.translateY}px, 0) scale(${mapState.zoom})`;
                break;
            default:
                // Default to landscape
                mapImage.style.transform = `translate3d(${mapState.translateX}px, ${mapState.translateY}px, 0) scale(${mapState.zoom})`;
        }

        // Apply same transform to grid overlay to keep it aligned with map
        const gridOverlay = document.getElementById('grid-overlay');
        if (gridOverlay) {
            gridOverlay.style.transform = mapImage.style.transform;
        }

        // Apply same transform to camera icons container to keep icons aligned with map
        const cameraIconsContainer = document.getElementById('camera-icons-container');
        if (cameraIconsContainer) {
            cameraIconsContainer.style.transform = mapImage.style.transform;
        }

        // Update zoom level display
        const zoomPercent = Math.round((mapState.zoom - 1) * 100);
        zoomLevelDisplay.textContent = `${zoomPercent}%`;

        // Update camera markers to match new map transform
        updateCameraMarkers();

        console.log(`Map transform updated: orientation=${mapState.orientation}, translate(${mapState.translateX}px, ${mapState.translateY}px) scale(${mapState.zoom})`);
    }

    // Change map style
    function changeMapStyle(mapType) {
        // Only update if the map type has changed
        if (mapState.mapType === mapType) return;

        mapState.mapType = mapType;

        // Get the selected map option and its data
        const selectedOption = document.querySelector(`.map-option[data-map="${mapType}"]`);
        const accuracy = selectedOption ? selectedOption.getAttribute('data-accuracy') : '100%';
        const quality = selectedOption ? selectedOption.getAttribute('data-quality') : 'High';
        const type = selectedOption ? selectedOption.getAttribute('data-type') : 'Standard';

        // Update map type display in both places
        if (currentMapType) {
            currentMapType.textContent = type;
        }

        if (currentMapDisplay) {
            currentMapDisplay.textContent = type;
        }

        // Remove previous map classes
        mapContainer.classList.remove('dark-map', 'light-map', 'satellite-map', 'terrain-map');

        // Add new map class
        mapContainer.classList.add(`${mapType}-map`);

        // Update the image source
        if (mapType === 'satellite' || mapType === 'terrain') {
            mapImage.src = mapType === 'satellite' ? 'img/map_dark.png' : 'img/map_light.png';
        } else {
            mapImage.src = `img/map_${mapType}.png`;
        }

        console.log(`Changed map style to: ${mapType} (${type}, Accuracy: ${accuracy}, Quality: ${quality})`);
    }

    // Update date and time display
    function updateDateTime() {
        const dateTimeElement = document.querySelector('.date-time');
        if (dateTimeElement) {
            const now = new Date();
            const dateTimeStr = now.toLocaleDateString() + ' ' +
                               now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            dateTimeElement.textContent = dateTimeStr;
        }
    }

    // Initialize the map
    function initializeMap() {
        console.log('Initializing map with default settings...');

        try {
            // IMPORTANT: Set orientation first to avoid flicker
            // Set initial map orientation to portrait_flipped (default)
            mapState.orientation = 'portrait_flipped';
            rotateOrientationBtn.classList.remove('active'); // Ensure button is not active
            console.log('Map orientation set to portrait_flipped (default)');

            // Set initial map type to satellite
            mapState.mapType = 'satellite';

            // Get the selected map option and its data
            const satelliteOption = document.querySelector('.map-option[data-map="satellite"]');
            const type = satelliteOption ? satelliteOption.getAttribute('data-type') : 'Graphic';

            // Update map type displays
            if (currentMapType) {
                currentMapType.textContent = type;
            }

            if (currentMapDisplay) {
                currentMapDisplay.textContent = type;
            }

            // Make sure the satellite map option is active
            const satelliteMapOption = document.querySelector('.map-option[data-map="satellite"]');
            if (satelliteMapOption) {
                mapOptions.forEach(opt => opt.classList.remove('active'));
                satelliteMapOption.classList.add('active');
            }

            // Make sure grid is visible and button is active
            toggleGridBtn.classList.add('active');
            gridOverlay.classList.remove('hidden');
            gridOverlay.style.opacity = '1';
            mapState.gridEnabled = true;

            // Make sure map container has the correct class
            mapContainer.classList.remove('dark-map', 'light-map', 'terrain-map');
            mapContainer.classList.add('satellite-map');

            // Reset map position and zoom
            mapState.translateX = 0;
            mapState.translateY = 0;
            mapState.zoom = 1.5; // Increased default zoom for better visibility

            // Set initial zoom level display
            const initialZoomPercent = Math.round((mapState.zoom - 1) * 100);
            zoomLevelDisplay.textContent = `${initialZoomPercent}%`;

            // Make sure the map selector is hidden initially
            mapStyleSelector.classList.add('hidden');

            // Reset all icon buttons to default state except grid button
            document.querySelectorAll('.icon-btn').forEach(btn => {
                if (btn !== toggleGridBtn) { // Keep grid button active
                    btn.classList.remove('active');
                }
            });

            // Apply the initial transform immediately to avoid flicker
            // This needs to happen after all state is set
            updateMapTransform();

            // Set default cursor
            mapContainer.style.cursor = 'default';

            // Update date and time
            updateDateTime();

            // Set interval to update date and time every minute
            setInterval(updateDateTime, 60000);

            // Initialize camera markers
            renderCameraMarkers();

            // Initialize direction indicators
            updateDirectionIndicators();

            // Preload map preview images in the background for faster menu opening
            setTimeout(() => {
                const previewImages = [
                    'img/preview/map_dark_preview.png',
                    'img/preview/map_light_preview.png'
                ];

                previewImages.forEach(src => {
                    const img = new Image();
                    img.src = src;
                });

                console.log('Map preview images preloaded');
            }, 1000); // Delay preloading to prioritize initial rendering

            console.log('Map initialized successfully');
        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    // Render camera markers on the map
    function renderCameraMarkers() {
        // Remove existing markers
        document.querySelectorAll('.camera-marker').forEach(marker => marker.remove());

        // Remove existing tooltips
        document.querySelectorAll('.camera-tooltip').forEach(tooltip => tooltip.remove());

        // Get map container dimensions
        const mapRect = mapContainer.getBoundingClientRect();

        // Create and position markers for each camera
        mapState.cameras.forEach(camera => {
            // Create marker element
            const marker = document.createElement('div');
            marker.className = `camera-marker ${camera.type}`;
            if (!camera.active) marker.classList.add('inactive');
            if (mapState.selectedCamera === camera.id) marker.classList.add('selected');

            // Add camera icon based on location type
            const icon = document.createElement('i');
            icon.className = `fas ${camera.icon}`;
            marker.appendChild(icon);

            // Convert FiveM coordinates to map coordinates using your transformation
            // Based on your Leaflet code: new L.Transformation(scale_x, center_x, -scale_y, center_y)
            const mapX = (camera.x * scale_x) + center_x;
            const mapY = (camera.y * -scale_y) + center_y;

            // Convert to pixel coordinates relative to map size
            const posX = (mapX / 256) * mapRect.width;  // 256 is typical tile size
            const posY = (mapY / 256) * mapRect.height;

            // Position marker
            marker.style.left = `${posX}px`;
            marker.style.top = `${posY}px`;

            // Add data attributes
            marker.dataset.cameraId = camera.id;
            marker.dataset.cameraType = camera.type;
            marker.dataset.cameraName = camera.name;

            // Add event listeners
            marker.addEventListener('click', () => selectCamera(camera.id));
            marker.addEventListener('mouseenter', (e) => showCameraTooltip(e, camera));
            marker.addEventListener('mouseleave', hideCameraTooltip);

            // Add marker to map image container to ensure it moves with the map
            document.getElementById('camera-icons-container').appendChild(marker);
        });

        console.log(`Rendered ${mapState.cameras.length} camera markers`);
    }

    // Show camera tooltip on hover
    function showCameraTooltip(event, camera) {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'camera-tooltip';
        tooltip.id = `tooltip-${camera.id}`;

        // Set tooltip content
        tooltip.innerHTML = `
            <strong>${camera.name}</strong><br>
            Location Type: ${camera.type.charAt(0).toUpperCase() + camera.type.slice(1)}<br>
            Status: ${camera.active ? '<span style="color: #4CAF50">Active</span>' : '<span style="color: #F44336">Inactive</span>'}
        `;

        // Get marker position
        const markerRect = event.target.getBoundingClientRect();
        const mapContainerRect = mapContainer.getBoundingClientRect();

        // Calculate position relative to map container
        const tooltipX = markerRect.left - mapContainerRect.left + (markerRect.width / 2);
        const tooltipY = markerRect.top - mapContainerRect.top;

        // Position tooltip above marker
        tooltip.style.left = `${tooltipX}px`;
        tooltip.style.top = `${tooltipY}px`;

        // Add tooltip to map container
        mapContainer.appendChild(tooltip);
    }

    // Hide camera tooltip
    function hideCameraTooltip() {
        document.querySelectorAll('.camera-tooltip').forEach(tooltip => tooltip.remove());
    }

    // Select camera
    function selectCamera(cameraId) {
        // Get the camera data
        const camera = mapState.cameras.find(cam => cam.id === cameraId);
        if (!camera) return;

        // Open camera view directly
        openCameraView(camera);

        console.log(`Opening camera ${cameraId}: ${camera.name}`);
    }

    // Open camera view
    function openCameraView(camera) {
        // Hide the main app
        document.getElementById('app').classList.add('hidden');

        // Show camera view
        const cameraView = document.getElementById('camera-view');
        if (cameraView) {
            cameraView.classList.remove('hidden');

            // Update camera timestamp
            const cameraTimestamp = document.getElementById('camera-timestamp');
            if (cameraTimestamp) {
                const now = new Date();
                const options = {
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                    second: 'numeric',
                    hour12: true
                };
                cameraTimestamp.textContent = now.toLocaleString('en-US', options);
            }

            console.log(`Camera view opened for ${camera.name}`);
        }
    }

    // Update camera markers when map is resized or transformed
    function updateCameraMarkers() {
        // Get map container dimensions
        const mapRect = mapContainer.getBoundingClientRect();

        // Update position of each marker
        document.querySelectorAll('.camera-marker').forEach(marker => {
            const cameraId = parseInt(marker.dataset.cameraId);
            const camera = mapState.cameras.find(cam => cam.id === cameraId);

            if (camera) {
                // Convert FiveM coordinates to map coordinates using your transformation
                const mapX = (camera.x * scale_x) + center_x;
                const mapY = (camera.y * -scale_y) + center_y;

                // Convert to pixel coordinates relative to map size
                const posX = (mapX / 256) * mapRect.width;  // 256 is typical tile size
                const posY = (mapY / 256) * mapRect.height;

                // Update marker position
                marker.style.left = `${posX}px`;
                marker.style.top = `${posY}px`;
            }
        });

        // Remove any tooltips
        hideCameraTooltip();
    }

    // Update direction indicators based on map orientation
    function updateDirectionIndicators() {
        // Get all direction indicators
        const northIndicator = document.getElementById('north-indicator');
        const southIndicator = document.getElementById('south-indicator');
        const eastIndicator = document.getElementById('east-indicator');
        const westIndicator = document.getElementById('west-indicator');
        const northeastIndicator = document.getElementById('northeast-indicator');
        const northwestIndicator = document.getElementById('northwest-indicator');
        const southeastIndicator = document.getElementById('southeast-indicator');
        const southwestIndicator = document.getElementById('southwest-indicator');

        // Update positions based on orientation
        switch (mapState.orientation) {
            case 'landscape':
                // Standard landscape mode
                northIndicator.style.top = '30px';
                northIndicator.style.left = '50%';
                northIndicator.style.right = 'auto';
                northIndicator.style.bottom = 'auto';
                northIndicator.style.transform = 'translateX(-50%)';

                southIndicator.style.bottom = '5px';
                southIndicator.style.left = '50%';
                southIndicator.style.right = 'auto';
                southIndicator.style.top = 'auto';
                southIndicator.style.transform = 'translateX(-50%)';

                eastIndicator.style.right = '5px';
                eastIndicator.style.top = '50%';
                eastIndicator.style.left = 'auto';
                eastIndicator.style.bottom = 'auto';
                eastIndicator.style.transform = 'translateY(-50%)';

                westIndicator.style.left = '5px';
                westIndicator.style.top = '50%';
                westIndicator.style.right = 'auto';
                westIndicator.style.bottom = 'auto';
                westIndicator.style.transform = 'translateY(-50%)';

                // Corner indicators
                northeastIndicator.style.top = '30px';
                northeastIndicator.style.right = '5px';
                northeastIndicator.style.left = 'auto';
                northeastIndicator.style.bottom = 'auto';

                northwestIndicator.style.top = '30px';
                northwestIndicator.style.left = '5px';
                northwestIndicator.style.right = 'auto';
                northwestIndicator.style.bottom = 'auto';

                southeastIndicator.style.bottom = '5px';
                southeastIndicator.style.right = '5px';
                southeastIndicator.style.left = 'auto';
                southeastIndicator.style.top = 'auto';

                southwestIndicator.style.bottom = '5px';
                southwestIndicator.style.left = '5px';
                southwestIndicator.style.right = 'auto';
                southwestIndicator.style.top = 'auto';
                break;

            case 'landscape_flipped':
                // Flipped landscape mode (180 degrees)
                northIndicator.style.bottom = '5px';
                northIndicator.style.left = '50%';
                northIndicator.style.right = 'auto';
                northIndicator.style.top = 'auto';
                northIndicator.style.transform = 'translateX(-50%)';

                southIndicator.style.top = '30px';
                southIndicator.style.left = '50%';
                southIndicator.style.right = 'auto';
                southIndicator.style.bottom = 'auto';
                southIndicator.style.transform = 'translateX(-50%)';

                eastIndicator.style.left = '5px';
                eastIndicator.style.top = '50%';
                eastIndicator.style.right = 'auto';
                eastIndicator.style.bottom = 'auto';
                eastIndicator.style.transform = 'translateY(-50%)';

                westIndicator.style.right = '5px';
                westIndicator.style.top = '50%';
                westIndicator.style.left = 'auto';
                westIndicator.style.bottom = 'auto';
                westIndicator.style.transform = 'translateY(-50%)';

                // Corner indicators
                northeastIndicator.style.bottom = '5px';
                northeastIndicator.style.left = '5px';
                northeastIndicator.style.right = 'auto';
                northeastIndicator.style.top = 'auto';

                northwestIndicator.style.bottom = '5px';
                northwestIndicator.style.right = '5px';
                northwestIndicator.style.left = 'auto';
                northwestIndicator.style.top = 'auto';

                southeastIndicator.style.top = '30px';
                southeastIndicator.style.left = '5px';
                southeastIndicator.style.right = 'auto';
                southeastIndicator.style.bottom = 'auto';

                southwestIndicator.style.top = '30px';
                southwestIndicator.style.right = '5px';
                southwestIndicator.style.left = 'auto';
                southwestIndicator.style.bottom = 'auto';
                break;

            case 'portrait':
                // Standard portrait mode (90 degrees)
                northIndicator.style.right = '5px';
                northIndicator.style.top = '50%';
                northIndicator.style.left = 'auto';
                northIndicator.style.bottom = 'auto';
                northIndicator.style.transform = 'translateY(-50%)';

                southIndicator.style.left = '5px';
                southIndicator.style.top = '50%';
                southIndicator.style.right = 'auto';
                southIndicator.style.bottom = 'auto';
                southIndicator.style.transform = 'translateY(-50%)';

                eastIndicator.style.top = '30px';
                eastIndicator.style.left = '50%';
                eastIndicator.style.right = 'auto';
                eastIndicator.style.bottom = 'auto';
                eastIndicator.style.transform = 'translateX(-50%)';

                westIndicator.style.bottom = '5px';
                westIndicator.style.left = '50%';
                westIndicator.style.right = 'auto';
                westIndicator.style.top = 'auto';
                westIndicator.style.transform = 'translateX(-50%)';

                // Corner indicators
                northeastIndicator.style.right = '5px';
                northeastIndicator.style.top = '30px';
                northeastIndicator.style.left = 'auto';
                northeastIndicator.style.bottom = 'auto';

                northwestIndicator.style.right = '5px';
                northwestIndicator.style.bottom = '5px';
                northwestIndicator.style.left = 'auto';
                northwestIndicator.style.top = 'auto';

                southeastIndicator.style.left = '5px';
                southeastIndicator.style.top = '30px';
                southeastIndicator.style.right = 'auto';
                southeastIndicator.style.bottom = 'auto';

                southwestIndicator.style.left = '5px';
                southwestIndicator.style.bottom = '5px';
                southwestIndicator.style.right = 'auto';
                southwestIndicator.style.top = 'auto';
                break;

            case 'portrait_flipped':
            default:
                // Flipped portrait mode (270 degrees)
                northIndicator.style.left = '5px';
                northIndicator.style.top = '50%';
                northIndicator.style.right = 'auto';
                northIndicator.style.bottom = 'auto';
                northIndicator.style.transform = 'translateY(-50%)';

                southIndicator.style.right = '5px';
                southIndicator.style.top = '50%';
                southIndicator.style.left = 'auto';
                southIndicator.style.bottom = 'auto';
                southIndicator.style.transform = 'translateY(-50%)';

                eastIndicator.style.bottom = '5px';
                eastIndicator.style.left = '50%';
                eastIndicator.style.right = 'auto';
                eastIndicator.style.top = 'auto';
                eastIndicator.style.transform = 'translateX(-50%)';

                westIndicator.style.top = '30px';
                westIndicator.style.left = '50%';
                westIndicator.style.right = 'auto';
                westIndicator.style.bottom = 'auto';
                westIndicator.style.transform = 'translateX(-50%)';

                // Corner indicators
                northeastIndicator.style.left = '5px';
                northeastIndicator.style.top = '30px';
                northeastIndicator.style.right = 'auto';
                northeastIndicator.style.bottom = 'auto';

                northwestIndicator.style.left = '5px';
                northwestIndicator.style.bottom = '5px';
                northwestIndicator.style.right = 'auto';
                northwestIndicator.style.top = 'auto';

                southeastIndicator.style.right = '5px';
                southeastIndicator.style.top = '30px';
                southeastIndicator.style.left = 'auto';
                southeastIndicator.style.bottom = 'auto';

                southwestIndicator.style.right = '5px';
                southwestIndicator.style.bottom = '5px';
                southwestIndicator.style.left = 'auto';
                southwestIndicator.style.top = 'auto';
                break;
        }
    }

    // Close app button functionality
    const closeAppBtn = document.getElementById('close-app');
    if (closeAppBtn) {
        closeAppBtn.addEventListener('click', () => {
            console.log('Close app button clicked');
            // In a real FiveM environment, this would close the UI
            // For this demo, we'll just add a visual effect
            document.body.classList.add('closing');
            setTimeout(() => {
                alert('Application would close in FiveM environment');
                document.body.classList.remove('closing');
            }, 300);
        });
    }

    // Mouse move for FiveM coordinates display
    document.addEventListener('mousemove', (e) => {
        // Only update coordinates if mouse is over the map
        const mapRect = mapImage.getBoundingClientRect();
        if (e.clientX >= mapRect.left && e.clientX <= mapRect.right &&
            e.clientY >= mapRect.top && e.clientY <= mapRect.bottom) {

            // Calculate relative position within the map (0-1)
            const relativeX = (e.clientX - mapRect.left) / mapRect.width;
            const relativeY = (e.clientY - mapRect.top) / mapRect.height;

            // Convert to map coordinates (similar to your Leaflet transformation but reversed)
            const mapX = (relativeX * 256); // 256 is typical tile size
            const mapY = (relativeY * 256);

            // Convert back to FiveM coordinates using inverse transformation
            const fivemX = ((mapX - center_x) / scale_x).toFixed(2);
            const fivemY = ((mapY - center_y) / -scale_y).toFixed(2);

            // Update coordinates display with FiveM coordinates
            const mouseCoordinatesDisplay = document.getElementById('mouse-coordinates');
            if (mouseCoordinatesDisplay) {
                mouseCoordinatesDisplay.textContent = `X: ${fivemX} Y: ${fivemY}`;
            }
        }
    });

    // Initialize camera functionality
    initializeCameraControls();

    // Call initialization
    initializeMap();
});

// Initialize camera controls
function initializeCameraControls() {
    // Camera elements
    const cameraView = document.getElementById('camera-view');
    const cameraFeed = document.getElementById('camera-feed');
    const closeCamera = document.getElementById('close-camera');
    const camZoomIn = document.getElementById('cam-zoom-in');
    const camZoomOut = document.getElementById('cam-zoom-out');
    const camFiles = document.getElementById('cam-files');
    const camScreenshot = document.getElementById('cam-screenshot');
    const camNightvision = document.getElementById('cam-nightvision');
    const camPrev = document.getElementById('cam-prev');
    const camNext = document.getElementById('cam-next');
    const cameraTimestamp = document.getElementById('camera-timestamp');

    // Camera state
    let cameraState = {
        zoom: 1,
        nightvision: false,
        currentCameraIndex: 0
    };

    // Update timestamp
    function updateTimestamp() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'numeric',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
            hour12: true
        };
        cameraTimestamp.textContent = now.toLocaleString('en-US', options);
    }

    // Update timestamp every second
    setInterval(updateTimestamp, 1000);

    // Close camera view
    if (closeCamera) {
        closeCamera.addEventListener('click', () => {
            cameraView.classList.add('hidden');
            document.getElementById('app').classList.remove('hidden');
            console.log('Camera view closed');
        });
    }

    // Zoom in
    if (camZoomIn) {
        camZoomIn.addEventListener('click', () => {
            if (cameraState.zoom < 2) {
                cameraState.zoom += 0.1;
                cameraFeed.style.transform = `scale(${cameraState.zoom})`;
                console.log(`Zoomed in: ${cameraState.zoom.toFixed(1)}`);
            }
        });
    }

    // Zoom out
    if (camZoomOut) {
        camZoomOut.addEventListener('click', () => {
            if (cameraState.zoom > 0.5) {
                cameraState.zoom -= 0.1;
                cameraFeed.style.transform = `scale(${cameraState.zoom})`;
                console.log(`Zoomed out: ${cameraState.zoom.toFixed(1)}`);
            }
        });
    }

    // Toggle night vision
    if (camNightvision) {
        camNightvision.addEventListener('click', () => {
            cameraState.nightvision = !cameraState.nightvision;
            if (cameraState.nightvision) {
                cameraFeed.classList.add('nightvision');
                console.log('Night vision enabled');
            } else {
                cameraFeed.classList.remove('nightvision');
                console.log('Night vision disabled');
            }
        });
    }

    // Take screenshot
    if (camScreenshot) {
        camScreenshot.addEventListener('click', () => {
            // Flash effect
            const flash = document.createElement('div');
            flash.className = 'camera-flash';
            cameraFeed.appendChild(flash);

            setTimeout(() => {
                cameraFeed.removeChild(flash);
                console.log('Screenshot taken');
            }, 500);
        });
    }

    // Navigate between cameras
    if (camPrev) {
        camPrev.addEventListener('click', () => {
            if (cameraState.currentCameraIndex > 0) {
                cameraState.currentCameraIndex--;
                console.log(`Switched to camera ${cameraState.currentCameraIndex + 1}`);
            }
        });
    }

    if (camNext) {
        camNext.addEventListener('click', () => {
            if (cameraState.currentCameraIndex < 9) { // Assuming 10 cameras (0-9)
                cameraState.currentCameraIndex++;
                console.log(`Switched to camera ${cameraState.currentCameraIndex + 1}`);
            }
        });
    }

    // Open files panel
    if (camFiles) {
        camFiles.addEventListener('click', () => {
            console.log('Files panel opened');
        });
    }

    // Show camera view when clicking on camera icons
    const cameraIcons = document.querySelectorAll('.camera-marker');
    cameraIcons.forEach((icon, index) => {
        icon.addEventListener('click', () => {
            // Hide the main app
            document.getElementById('app').classList.add('hidden');

            // Show camera view
            cameraView.classList.remove('hidden');
            cameraState.currentCameraIndex = index;
            console.log(`Opened camera ${index + 1}`);

            // Reset camera state
            cameraState.zoom = 1;
            cameraFeed.style.transform = 'scale(1)';

            // Update timestamp
            updateTimestamp();

            // Set camera name based on the camera type
            const cameraName = mapState.cameras[index].name;
            console.log(`Camera location: ${cameraName}`);
        });
    });
}