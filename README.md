# Advanced Police Cams for FiveM

This project implements a surveillance camera system for FiveM, allowing players to view and control security cameras throughout the city.

## Features

- Interactive map with camera locations
- Camera control interface (pan, tilt, zoom, rotate)
- Live camera feeds
- Recording functionality

## Integration with Lua

The web interface is designed to be integrated with Lua scripts in FiveM. Camera positions are defined using vector2 coordinates (x, y) which determine where camera icons appear on the map.

### Expected Lua Integration

```lua
-- Example of how <PERSON><PERSON> would interact with the web interface
function SendCameraDataToUI()
    local cameraData = {
        {id = 1, x = 20, y = 20, location = "Sandy Shores Police Department"},
        {id = 2, x = 30, y = 40, location = "Paleto Bay Sheriff's Office"},
        -- More cameras...
    }
    
    -- Send data to the UI
    SendNUIMessage({
        type = "updateCameras",
        cameras = cameraData
    })
end

-- Example of how UI would call back to Lua
RegisterNUICallback('openCamera', function(data, cb)
    local cameraId = data.id
    -- Logic to handle camera opening in the game
    cb({success = true})
end)
```

## Setup

1. Place the files in your FiveM resource folder
2. Add the resource to your server.cfg
3. Integrate with your existing police/security systems

## Usage

- Access the camera system through an in-game computer or tablet
- Click on camera icons to view feeds
- Use the control buttons to manipulate the camera view
