/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: transparent;
    color: #fff;
    overflow: hidden;
}

/* App container with responsive sizing */
#app {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Interface frame with border */
.interface-frame {
    position: relative;
    overflow: hidden;
    background-color: transparent;
    border: 2px solid #000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Map background colors based on map type */
.dark-map {
    background-color: #374960; /* Dark map sea color */
}

.light-map {
    background-color: #9dc0fa; /* Light map sea color */
}

/* Header bar */
.header-bar {
    background-color: #000000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    height: 30px;
}

/* Sidebar panels */
.sidebar-panel {
    width: 250px;
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 10px;
    overflow: hidden;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Main container layout */
#main-container {
    display: flex;
    overflow: hidden;
}

/* Right side logs panel */
.logs-panel {
    width: 250px;
    height: calc(100% - 30px); /* Subtract header bar height */
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 30px; /* Add margin to account for header bar */
}

.log-section {
    margin-bottom: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.log-header {
    background-color: #000000;
    color: white;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-content {
    flex: 1;
    padding: 5px;
    overflow-y: auto;
}

.refresh-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.refresh-btn:hover {
    color: #4a9df8;
}

.log-item {
    margin-bottom: 8px;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.log-time {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 2px;
}

.log-info {
    font-size: 12px;
    color: white;
}

.panel-header {
    background-color: #000000;
    padding: 5px 10px;
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
    padding: 10px;
    max-height: 120px;
    overflow-y: auto;
}

/* Log entries */
.log-entry {
    padding: 4px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    font-size: 12px;
}

.log-time {
    color: #4CAF50;
    margin-right: 8px;
    font-family: monospace;
}

.log-message {
    color: #e0e0e0;
}
/* Right sidebar panels */
.sidebar-panel:not(.left-panel) {
    width: 200px;
    background-color: #000000;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Panel header for right panels */
.sidebar-panel:not(.left-panel) .panel-header {
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Panel content for right panels */
.sidebar-panel:not(.left-panel) .panel-content {
    height: 150px;
    overflow-y: auto;
    padding: 5px;
}

.control-group {
    display: flex;
    align-items: center;
}

.control-label {
    margin-left: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

/* Header Bar */
.header-bar {
    background-color: #000000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    height: 30px;
}

/* Control Bar */
.control-bar {
    display: flex;
    margin-bottom: 3px;
}

/* Icon Buttons */
.icon-btn {
    width: 28px;
    height: 28px;
    background-color: rgba(173, 216, 230, 0.2); /* Light blue transparent background */
    border: 1px solid rgba(173, 216, 230, 0.4);
    border-radius: 2px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 12px;
    user-select: none; /* Prevent text selection */
    margin: 0 1px;
    position: relative;
    backdrop-filter: blur(2px);
}

.icon-btn:hover {
    background-color: rgba(173, 216, 230, 0.3);
    border-color: rgba(173, 216, 230, 0.5);
}

.icon-btn:active {
    transform: scale(0.95);
}

.icon-btn.active {
    background-color: rgba(0, 120, 255, 0.4);
    border-color: rgba(0, 120, 255, 0.6);
    box-shadow: 0 0 5px rgba(0, 120, 255, 0.5);
}

/* Status Information */
.status-info {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-align: right;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.status-info.hidden {
    opacity: 0;
    visibility: hidden;
}

.date-time {
    color: rgba(173, 216, 230, 0.8); /* Light blue for date/time */
}



/* Camera Icons */
.camera-icon {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: translate(-50%, -50%);
    transition: all 0.2s;
    z-index: 5;
}

.camera-icon:hover {
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

.camera-icon::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: black;
    border-radius: 50%;
}

.camera-icon.active {
    background-color: #4CAF50;
}

/* Camera location tooltip */
.camera-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s;
    transform: translateY(-100%);
    margin-top: -10px;
    white-space: nowrap;
    z-index: 10;
}

.camera-icon:hover .camera-tooltip {
    opacity: 1;
}

/* Camera frame border */
.camera-frame-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(255, 255, 255, 0.5);
    pointer-events: none;
    z-index: 5;
}

.camera-frame-border::before,
.camera-frame-border::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    pointer-events: none;
}

.camera-frame-border::before {
    top: -2px;
    left: -2px;
    border-right: none;
    border-bottom: none;
}

.camera-frame-border::after {
    bottom: -2px;
    right: -2px;
    border-left: none;
    border-top: none;
}

/* Camera Controls */
.camera-control-btn {
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 12px;
}

.camera-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Recording indicator */
.recording-dot {
    width: 10px;
    height: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Camera view background */
#camera-view {
    background-color: #7d7370; /* Match the gray-brown color from the image */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
}

/* Night vision effect */
.nightvision {
    filter: sepia(50%) hue-rotate(90deg) saturate(200%) brightness(150%);
}

/* Camera flash effect */
.camera-flash {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    opacity: 0;
    animation: flash 0.5s ease-out;
    pointer-events: none;
    z-index: 100;
}

@keyframes flash {
    0% { opacity: 0; }
    10% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Map container and image */
#map-container {
    cursor: default;
    /* Only use will-change when actually changing to avoid memory consumption */
    transform: translate3d(0, 0, 0); /* Force hardware acceleration */
    backface-visibility: hidden;
    perspective: 1000;
    position: relative;
    width: 100%;
    height: 100%;
}

#map-container.draggable {
    cursor: grab;
    will-change: transform; /* Only add will-change when needed */
}

#map-container.draggable:active {
    cursor: grabbing;
}

#map-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform: translate3d(0, 0, 0) rotate(270deg); /* Force hardware acceleration and set initial rotation to portrait_flipped */
    backface-visibility: hidden; /* Prevent flickering */
    perspective: 1000; /* Improve performance */
    will-change: transform; /* Optimize for transforms */
    transition: none; /* No transition for immediate response */
    transform-origin: center; /* Ensure rotation happens from center */
}

/* Camera feed styling */
#camera-feed {
    background-color: #111;
    background-image: url('https://via.placeholder.com/1920x1080/111111/333333?text=Camera+Feed');
    background-size: cover;
    background-position: center;
}

/* Camera markers */
.camera-marker {
    position: absolute;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 10;
    transition: transform 0.2s ease;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.7));
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-marker:hover {
    transform: translate(-50%, -50%) scale(1.2);
    z-index: 11;
}

.camera-marker.selected {
    transform: translate(-50%, -50%) scale(1.3);
    z-index: 12;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

/* Camera types with different icons and colors */
.camera-marker.store {
    color: #4CAF50; /* Green */
}

.camera-marker.jewelry {
    color: #FFC107; /* Gold/Yellow */
}

.camera-marker.bank {
    color: #2196F3; /* Blue */
}

.camera-marker.gas {
    color: #FF5722; /* Orange/Red */
}

.camera-marker.police {
    color: #3F51B5; /* Indigo */
}

.camera-marker.hospital {
    color: #F44336; /* Red */
}

.camera-marker.bar {
    color: #9C27B0; /* Purple */
}

.camera-marker.apartment {
    color: #795548; /* Brown */
}

.camera-marker.park {
    color: #8BC34A; /* Light Green */
}

.camera-marker.beach {
    color: #00BCD4; /* Cyan */
}

.camera-marker.inactive {
    opacity: 0.5;
}

/* Camera icons container */
#camera-icons-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
    pointer-events: none;
}

#camera-icons-container .camera-marker {
    pointer-events: auto;
}

/* Camera info tooltip */
.camera-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    white-space: nowrap;
    z-index: 100;
    transform: translate(-50%, -100%);
    margin-top: -10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

/* Camera location highlight */
.camera-location-highlight {
    position: absolute;
    background-color: rgba(0, 255, 0, 0.2);
    border: 1px solid rgba(0, 255, 0, 0.5);
    border-radius: 4px;
    padding: 5px 10px;
    pointer-events: none;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Map Style Selector */
.map-presets-panel {
    width: 180px;
    background-color: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 8px;
    z-index: 100;
    position: absolute;
    top: 50px; /* Position below the control bar */
    right: 10px; /* Align with the right side */
    transition: all 0.15s ease;
    opacity: 1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
    will-change: transform, opacity;
    backface-visibility: hidden;
    contain: content; /* Improve performance by isolating the content */
}

.map-presets-panel.hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateY(-5px);
    transition: all 0.15s ease;
    visibility: hidden;
}

.map-title {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #7fbbff;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding-bottom: 8px;
}

.map-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.map-option {
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    transition: all 0.15s;
    height: 60px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 5px;
}

.map-option:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.map-option.active {
    border-color: #7fbbff;
    box-shadow: 0 0 6px rgba(127, 187, 255, 0.7);
}

.map-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    will-change: transform;
    transform: translateZ(0); /* Force GPU acceleration */
    backface-visibility: hidden;
}

.map-info-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 3px 5px;
    font-size: 9px;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    gap: 1px;
    opacity: 0;
    transition: opacity 0.15s ease;
}

.map-option:hover .map-info-overlay {
    opacity: 1;
}

/* Keep the old class for backward compatibility */
.map-option-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 3px 5px;
    font-size: 9px;
    color: rgba(255, 255, 255, 0.9);
    display: none;
}

/* Grid overlay styling */
#grid-overlay {
    pointer-events: none;
}

.grid-lines {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(to right, rgba(255, 255, 255, 0.15) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: 5;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    pointer-events: none; /* Ensure grid doesn't interfere with interactions */
    transition: opacity 0.3s ease;
}

/* Portrait mode transformations - REMOVED DUPLICATE */

.direction-indicator {
    position: absolute;
    color: white;
    font-weight: bold;
    text-shadow: 0 0 5px black;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    z-index: 6;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    padding: 2px 4px;
}

.main-direction {
    font-size: 14px;
}

.sub-direction {
    font-size: 12px;
}

/* Main directions - positioned at the edges */
#north-indicator {
    top: 30px; /* Below the black bar */
    left: 50%;
    transform: translateX(-50%);
}

#south-indicator {
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
}

#east-indicator {
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
}

#west-indicator {
    top: 50%;
    left: 5px;
    transform: translateY(-50%);
}

/* Intermediate directions - positioned at the corners */
#northeast-indicator {
    top: 30px; /* Below the black bar */
    right: 5px;
}

#northwest-indicator {
    top: 30px; /* Below the black bar */
    left: 5px;
}

#southeast-indicator {
    bottom: 5px;
    right: 5px;
}

#southwest-indicator {
    bottom: 5px;
    left: 5px;
}

/* Map container background colors for different map types */
#map-container.dark-map {
    background-color: #374960; /* Dark ocean color */
}

#map-container.light-map {
    background-color: #9dc0fa; /* Light ocean color */
}

#map-container.satellite-map {
    background-color: #374960; /* Same as dark map */
}

#map-container.terrain-map {
    background-color: #9dc0fa; /* Same as light map */
}

/* Landscape/Portrait orientation - only rotate the map image */
.portrait-mode #map-image {
    /* No !important to allow other transforms to work */
    transform-origin: center;
    will-change: transform;
    backface-visibility: hidden;
}

/* Media queries for responsive design */
@media (max-width: 1600px) {
    #map-view {
        transform: scale(0.9) translate3d(0, 0, 0);
        transform-origin: center;
    }
}

@media (max-width: 1200px) {
    #map-view {
        transform: scale(0.8) translate3d(0, 0, 0);
        transform-origin: center;
    }
}

@media (max-width: 900px) {
    #map-view {
        transform: scale(0.7) translate3d(0, 0, 0);
        transform-origin: center;
    }
}

/* Close app button styles */
#close-app {
    transition: all 0.2s ease;
}

#close-app:hover {
    transform: scale(1.1);
}

#close-app:active {
    transform: scale(0.95);
}

/* Closing animation */
body.closing {
    animation: fadeOut 0.3s ease forwards;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
