<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surveillance Cameras</title>
    <link rel="stylesheet" href="styles/styles.css">
    <!-- Tailwind CSS (local version) -->
    <link rel="stylesheet" href="styles/vendor/tailwind.min.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom JavaScript -->
    <script src="scripts/main.js" defer></script>
</head>
<body class="text-white overflow-hidden">
    <div id="app" class="flex items-center justify-center min-h-screen">
        <!-- Main Container - Map + Logs -->
        <div id="main-container" class="flex relative" style="width: 1600px; height: 900px; max-width: 100vw; max-height: 100vh;">
            <!-- Header Bar - Covers both map and logs -->
            <div class="absolute top-0 left-0 right-0 z-30 header-bar">
                <div class="flex justify-between items-center px-3 py-1">
                    <div class="text-sm font-bold flex items-center">
                        <i class="fas fa-video mr-1"></i> Cams Control
                    </div>

                    <div id="close-app" class="text-sm font-bold flex items-center cursor-pointer hover:text-red-500 transition-colors">
                        <i class="fas fa-times-circle text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- Main Map View - Left side -->
            <div id="map-view" class="relative interface-frame flex-1">

            <!-- Control Icons Bar - Below the header bar, right side -->
            <div class="absolute top-10 right-10 z-20">
                <!-- Control Icons -->
                <div class="control-bar">
                    <div class="flex items-center">


                        <!-- Zoom In Button -->
                        <button id="zoom-in" class="icon-btn" title="Zoom In">
                            <i class="fas fa-plus"></i>
                        </button>

                        <!-- Zoom Out Button -->
                        <button id="zoom-out" class="icon-btn" title="Zoom Out">
                            <i class="fas fa-minus"></i>
                        </button>

                        <!-- Grid Toggle Button -->
                        <button id="toggle-grid" class="icon-btn active" title="Toggle Grid">
                            <i class="fas fa-th"></i>
                        </button>

                        <!-- Orientation Rotate Button -->
                        <button id="rotate-orientation" class="icon-btn" title="Rotate Map">
                            <i class="fas fa-sync-alt"></i>
                        </button>

                        <!-- Map Style Selector Toggle -->
                        <button id="map-selector-toggle" class="icon-btn" title="Map Styles">
                            <i class="fas fa-map"></i>
                        </button>
                    </div>
                </div>

                <!-- Status Information - Below the control bar -->
                <div id="status-info" class="status-info">
                    <div class="text-xs text-right date-time">10/18/2023 3:12:14 PM</div>
                    <div class="text-xs text-right">Map: <span id="current-map-display">Satellite</span></div>
                    <div class="text-xs text-right">Zoom: <span id="zoom-level">0%</span></div>
                </div>
            </div>



            <!-- Map Style Selector Panel (Hidden by default) -->
            <div id="map-style-selector" class="absolute z-20 map-presets-panel hidden">
                <h3 class="map-title">Map Presets</h3>
                <div class="map-options">
                    <div class="map-option active" data-map="dark" data-accuracy="94%" data-quality="Medium" data-type="Cleared">
                        <img src="img/preview/map_dark_preview.png" alt="Dark Map" loading="lazy">
                        <div class="map-info-overlay">
                            <div>Accuracy: 94%</div>
                            <div>Quality: Medium</div>
                            <div>Type: Cleared</div>
                        </div>
                    </div>
                    <div class="map-option" data-map="light" data-accuracy="92%" data-quality="Medium" data-type="Satellite">
                        <img src="img/preview/map_light_preview.png" alt="Light Map" loading="lazy">
                        <div class="map-info-overlay">
                            <div>Accuracy: 92%</div>
                            <div>Quality: Medium</div>
                            <div>Type: Satellite</div>
                        </div>
                    </div>
                    <div class="map-option" data-map="satellite" data-accuracy="90%" data-quality="Low" data-type="Graphic">
                        <img src="img/preview/map_dark_preview.png" alt="Satellite Map" loading="lazy">
                        <div class="map-info-overlay">
                            <div>Accuracy: 90%</div>
                            <div>Quality: Low</div>
                            <div>Type: Graphic</div>
                        </div>
                    </div>
                    <div class="map-option" data-map="terrain" data-accuracy="88%" data-quality="Low" data-type="Outline">
                        <img src="img/preview/map_light_preview.png" alt="Terrain Map" loading="lazy">
                        <div class="map-info-overlay">
                            <div>Accuracy: 88%</div>
                            <div>Quality: Low</div>
                            <div>Type: Outline</div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="map-container" class="w-full h-full relative overflow-hidden dark-map">
                <img src="img/map_dark.png" id="map-image" class="absolute top-0 left-0 w-full h-full object-contain" fetchpriority="high" style="transform: rotate(270deg); transform-origin: center;">

                <!-- Grid overlay (always visible) -->
                <div id="grid-overlay" class="absolute inset-0">
                    <div class="grid-lines"></div>
                    <!-- Main directions -->
                    <div id="north-indicator" class="direction-indicator main-direction">N</div>
                    <div id="south-indicator" class="direction-indicator main-direction">S</div>
                    <div id="east-indicator" class="direction-indicator main-direction">E</div>
                    <div id="west-indicator" class="direction-indicator main-direction">W</div>

                    <!-- Intermediate directions -->
                    <div id="northeast-indicator" class="direction-indicator sub-direction">NE</div>
                    <div id="northwest-indicator" class="direction-indicator sub-direction">NW</div>
                    <div id="southeast-indicator" class="direction-indicator sub-direction">SE</div>
                    <div id="southwest-indicator" class="direction-indicator sub-direction">SW</div>
                </div>

                <!-- Camera icons will be dynamically added here -->
                <div id="camera-icons-container"></div>

                <!-- Map center coordinates display (hidden) -->
                <div class="absolute bottom-2 right-2 bg-black bg-opacity-50 px-2 py-1 rounded hidden">
                    <span id="map-center-coordinates">X: 0 Y: 0</span>
                </div>
            </div>
        </div>

        <!-- Camera View (initially hidden) -->
        <div id="camera-view" class="fixed inset-0 z-50 hidden">
            <div id="camera-feed" class="w-full h-full relative bg-gray-600">
                <!-- Camera feed background -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-2xl text-gray-400 opacity-50">Camera Feed</div>
                </div>

                <!-- Camera frame border -->
                <div class="camera-frame-border"></div>

                <!-- Top right timestamp and recording indicator -->
                <div class="absolute top-4 right-4 flex items-center gap-2 text-white">
                    <span id="camera-timestamp">9/2/2023 11:53:24 PM</span>
                    <div class="flex items-center">
                        <span class="text-white mr-1">RECORDING</span>
                        <span class="recording-dot"></span>
                    </div>
                </div>

                <!-- Bottom left camera controls -->
                <div class="absolute bottom-4 left-4 flex gap-1">
                    <button class="camera-control-btn" id="close-camera" title="Close Camera">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-screenshot" title="Take Screenshot">
                        <i class="fas fa-camera"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-nightvision" title="Toggle Night Vision">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-zoom-out" title="Zoom Out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-zoom-in" title="Zoom In">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-files" title="View Files">
                        <i class="fas fa-folder"></i>
                    </button>
                </div>

                <!-- Bottom right camera navigation -->
                <div class="absolute bottom-4 right-4 flex gap-1">
                    <button class="camera-control-btn" id="cam-prev" title="Previous Camera">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="camera-control-btn" id="cam-next" title="Next Camera">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

            <!-- Right Side Logs Panel -->
            <div id="right-side-logs" class="logs-panel">
                <!-- Files Panel -->
                <div class="log-section">
                    <div class="log-header">
                        <div class="flex items-center">
                            <i class="fas fa-folder mr-1"></i>
                            <span>Files</span>
                        </div>
                        <button class="refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="log-content">
                        <div class="log-item">
                            <div class="log-time">10/19/2023 3:15:18 PM</div>
                            <div class="log-info">Screenshot_001.jpg</div>
                        </div>
                        <div class="log-item">
                            <div class="log-time">10/19/2023 2:45:32 PM</div>
                            <div class="log-info">Screenshot_002.jpg</div>
                        </div>
                        <div class="log-item">
                            <div class="log-time">10/19/2023 2:30:15 PM</div>
                            <div class="log-info">Camera_4_Recording.mp4</div>
                        </div>
                        <div class="log-item">
                            <div class="log-time">10/19/2023 1:15:42 PM</div>
                            <div class="log-info">Camera_7_Recording.mp4</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
