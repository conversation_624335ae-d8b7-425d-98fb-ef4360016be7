/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #000;
    color: #fff;
    overflow: hidden;
}

/* Control Buttons */
.control-btn {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.control-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Camera Icons */
.camera-icon {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: translate(-50%, -50%);
    transition: all 0.2s;
    z-index: 5;
}

.camera-icon:hover {
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

.camera-icon::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: black;
    border-radius: 50%;
}

.camera-icon.active {
    background-color: #4CAF50;
}

/* Camera location tooltip */
.camera-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s;
    transform: translateY(-100%);
    margin-top: -10px;
    white-space: nowrap;
    z-index: 10;
}

.camera-icon:hover .camera-tooltip {
    opacity: 1;
}

/* Camera Controls */
.camera-control-btn {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.camera-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Recording indicator */
.recording-dot {
    width: 10px;
    height: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Map zoom and pan */
#map-image {
    transition: transform 0.3s ease;
}

/* Camera feed styling */
#camera-feed {
    background-color: #111;
    background-image: url('https://via.placeholder.com/1920x1080/111111/333333?text=Camera+Feed');
    background-size: cover;
    background-position: center;
}

/* Camera location highlight */
.camera-location-highlight {
    position: absolute;
    background-color: rgba(0, 255, 0, 0.2);
    border: 1px solid rgba(0, 255, 0, 0.5);
    border-radius: 4px;
    padding: 5px 10px;
    pointer-events: none;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}
