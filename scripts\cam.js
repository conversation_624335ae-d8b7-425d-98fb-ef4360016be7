// DOM Elements
const mapView = document.getElementById('map-view');
const cameraView = document.getElementById('camera-view');
const mapImage = document.getElementById('map-image');
const cameraIconsContainer = document.getElementById('camera-icons-container');
const zoomInBtn = document.getElementById('zoom-in');
const zoomOutBtn = document.getElementById('zoom-out');
const zoomLevelDisplay = document.getElementById('zoom-level');
const coordinatesDisplay = document.getElementById('coordinates');
const closeCameraBtn = document.getElementById('close-camera');
const cameraLocationDisplay = document.getElementById('camera-location');
const cameraTimestampDisplay = document.getElementById('camera-timestamp');

// Map state
let mapState = {
    zoom: 1,
    panX: 0,
    panY: 0,
    isDragging: false,
    dragStartX: 0,
    dragStartY: 0,
    currentCameraId: null
};

// Camera data - this would be populated from <PERSON><PERSON> in the actual implementation
// Each camera has x, y coordinates (vector2 from Lua) and a location name
const cameras = [
    { id: 1, x: 20, y: 20, location: "Sandy Shores Police Department" },
    { id: 2, x: 30, y: 40, location: "Paleto Bay Sheriff's Office" },
    { id: 3, x: 50, y: 30, location: "Downtown Vinewood" },
    { id: 4, x: 70, y: 60, location: "Los Santos Police Department" },
    { id: 5, x: 80, y: 20, location: "Vespucci Beach" },
    { id: 6, x: 40, y: 70, location: "Grapeseed" },
    { id: 7, x: 60, y: 80, location: "Mount Chiliad" },
    { id: 8, x: 25, y: 65, location: "Harmony" },
    { id: 9, x: 75, y: 45, location: "Davis" },
    { id: 10, x: 55, y: 55, location: "Mirror Park" },
    // More cameras would be added here from Lua data
];

// Initialize the application
function init() {
    renderCameraIcons();
    setupEventListeners();
    updateMapTransform();
    updateDateTime();
    
    // Update date/time every second
    setInterval(updateDateTime, 1000);
}

// Render camera icons on the map
function renderCameraIcons() {
    cameraIconsContainer.innerHTML = '';
    
    cameras.forEach(camera => {
        const cameraIcon = document.createElement('div');
        cameraIcon.className = 'camera-icon';
        cameraIcon.dataset.id = camera.id;
        
        // Position the camera icon based on x, y coordinates (percentage of map width/height)
        cameraIcon.style.left = `${camera.x}%`;
        cameraIcon.style.top = `${camera.y}%`;
        
        // Add tooltip with location name
        const tooltip = document.createElement('div');
        tooltip.className = 'camera-tooltip';
        tooltip.textContent = camera.location;
        cameraIcon.appendChild(tooltip);
        
        // Add click event to open camera view
        cameraIcon.addEventListener('click', () => openCameraView(camera));
        
        cameraIconsContainer.appendChild(cameraIcon);
    });
}

// Set up event listeners
function setupEventListeners() {
    // Zoom controls
    zoomInBtn.addEventListener('click', () => {
        mapState.zoom = Math.min(mapState.zoom + 0.1, 2);
        updateMapTransform();
    });
    
    zoomOutBtn.addEventListener('click', () => {
        mapState.zoom = Math.max(mapState.zoom - 0.1, 0.5);
        updateMapTransform();
    });
    
    // Map dragging
    mapImage.addEventListener('mousedown', (e) => {
        mapState.isDragging = true;
        mapState.dragStartX = e.clientX - mapState.panX;
        mapState.dragStartY = e.clientY - mapState.panY;
    });
    
    document.addEventListener('mousemove', (e) => {
        if (mapState.isDragging) {
            mapState.panX = e.clientX - mapState.dragStartX;
            mapState.panY = e.clientY - mapState.dragStartY;
            updateMapTransform();
        }
        
        // Update coordinates display
        const mapRect = mapImage.getBoundingClientRect();
        const x = Math.round(((e.clientX - mapRect.left) / mapRect.width) * 100);
        const y = Math.round(((e.clientY - mapRect.top) / mapRect.height) * 100);
        coordinatesDisplay.textContent = `X: ${x} Y: ${y}`;
    });
    
    document.addEventListener('mouseup', () => {
        mapState.isDragging = false;
    });
    
    // Close camera view
    closeCameraBtn.addEventListener('click', closeCameraView);
    
    // Camera control buttons (these would be connected to Lua functions in the actual implementation)
    document.getElementById('cam-left').addEventListener('click', () => console.log('Camera move left'));
    document.getElementById('cam-right').addEventListener('click', () => console.log('Camera move right'));
    document.getElementById('cam-up').addEventListener('click', () => console.log('Camera move up'));
    document.getElementById('cam-down').addEventListener('click', () => console.log('Camera move down'));
    document.getElementById('cam-zoom-in').addEventListener('click', () => console.log('Camera zoom in'));
    document.getElementById('cam-zoom-out').addEventListener('click', () => console.log('Camera zoom out'));
    document.getElementById('cam-rotate').addEventListener('click', () => console.log('Camera rotate'));
    document.getElementById('cam-files').addEventListener('click', () => console.log('Camera files'));
}

// Update map transform based on zoom and pan
function updateMapTransform() {
    mapImage.style.transform = `translate(${mapState.panX}px, ${mapState.panY}px) scale(${mapState.zoom})`;
    zoomLevelDisplay.textContent = `Zoom: ${Math.round((mapState.zoom - 1) * 100)}%`;
}

// Open camera view for a specific camera
function openCameraView(camera) {
    mapState.currentCameraId = camera.id;
    cameraView.classList.remove('hidden');
    mapView.classList.add('hidden');
    
    // Update camera location display
    cameraLocationDisplay.textContent = camera.location;
    
    // In a real implementation, this would trigger a Lua callback to start streaming the camera feed
    console.log(`Opening camera ${camera.id} at ${camera.location}`);
}

// Close camera view
function closeCameraView() {
    mapState.currentCameraId = null;
    cameraView.classList.add('hidden');
    mapView.classList.remove('hidden');
    
    // In a real implementation, this would trigger a Lua callback to stop streaming the camera feed
    console.log('Closing camera view');
}

// Update date and time display
function updateDateTime() {
    const now = new Date();
    const formattedDateTime = now.toLocaleString('en-US', {
        month: 'numeric',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true
    });
    
    cameraTimestampDisplay.textContent = formattedDateTime;
}

// Function to receive camera data from Lua (would be called by Lua in the actual implementation)
function updateCameraData(cameraData) {
    // Parse the camera data from Lua and update the cameras array
    // This is a placeholder for the actual implementation
    console.log('Received camera data from Lua:', cameraData);
    
    // Re-render camera icons with the new data
    renderCameraIcons();
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', init);
